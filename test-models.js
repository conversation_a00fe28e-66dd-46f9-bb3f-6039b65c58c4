import { fetch } from 'undici';

async function testModels() {
    try {
        console.log('Testing models endpoint...');
        const response = await fetch('http://localhost:3002/v1/models', {
            headers: {
                'Authorization': 'Bearer 123456'
            }
        });
        console.log('Models endpoint status:', response.status);
        const text = await response.text();
        console.log('Models response:', text);
        
        if (response.ok) {
            try {
                const json = JSON.parse(text);
                console.log('Available models:', json.data?.map(m => m.id) || json.models?.map(m => m.name));
            } catch (e) {
                console.log('Could not parse JSON response');
            }
        }
    } catch (error) {
        console.error('Models test failed:', error);
    }
}

testModels();
