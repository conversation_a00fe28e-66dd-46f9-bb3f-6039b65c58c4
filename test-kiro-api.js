import { fetch } from 'undici';

async function testKiroAPI() {
    const url = 'http://localhost:3002/v1/messages';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-ant-api03-kiro-proxy-local-server-key-123456'
    };
    
    const body = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 1024,
        messages: [
            {
                role: 'user',
                content: 'Hello, can you tell me what 2+2 equals?'
            }
        ]
    };

    try {
        console.log('Testing Kiro API with Claude Messages format...');
        console.log('URL:', url);
        console.log('Request body:', JSON.stringify(body, null, 2));
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(body)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));
        
        const responseText = await response.text();
        console.log('Response body:', responseText);
        
        if (response.ok) {
            try {
                const jsonResponse = JSON.parse(responseText);
                console.log('Parsed response:', JSON.stringify(jsonResponse, null, 2));
            } catch (e) {
                console.log('Response is not valid JSON');
            }
        }
        
    } catch (error) {
        console.error('Error testing API:', error);
    }
}

testKiroAPI();
