import { fetch } from 'undici';

async function testSimpleAuth() {
    const url = 'http://localhost:3003/v1/messages';
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-test-123456'
    };
    
    const body = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 1024,
        messages: [
            {
                role: 'user',
                content: 'Hello, can you tell me what 2+2 equals?'
            }
        ]
    };

    try {
        console.log('Testing with simple sk- token...');
        console.log('URL:', url);
        console.log('Auth header:', headers.Authorization);
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(body)
        });

        console.log('Response status:', response.status);
        
        const responseText = await response.text();
        console.log('Response body:', responseText);
        
    } catch (error) {
        console.error('Error testing API:', error);
    }
}

testSimpleAuth();
