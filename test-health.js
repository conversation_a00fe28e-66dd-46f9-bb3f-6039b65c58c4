import { fetch } from 'undici';

async function testHealth() {
    try {
        console.log('Testing health endpoint...');
        const response = await fetch('http://localhost:3002/health');
        console.log('Health check status:', response.status);
        const text = await response.text();
        console.log('Health check response:', text);
    } catch (error) {
        console.error('Health check failed:', error);
    }
}

testHealth();
